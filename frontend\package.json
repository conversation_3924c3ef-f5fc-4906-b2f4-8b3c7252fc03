{"name": "hdsc-query-frontend", "version": "1.0.0", "description": "太享查询系统前端 - Vue.js 3 现代化重构版本", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "chart.js": "^4.4.0", "vue-chartjs": "^5.3.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^23.0.0", "prettier": "^3.0.0", "typescript": "~5.2.0", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0", "vitest": "^1.0.0", "vue-tsc": "^1.8.19"}, "keywords": ["vue3", "typescript", "vite", "element-plus", "pinia", "financial-data", "query-system"], "author": "HDSC Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}