"""
数据验证工具
提供各种数据验证功能
"""

import re
from typing import Dict, Any, List
from datetime import datetime


def validate_login_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证登录数据"""
    errors = []
    
    if not data:
        return {'valid': False, 'errors': ['请求数据不能为空']}
    
    # 验证用户名
    username = data.get('username')
    if not username:
        errors.append('用户名不能为空')
    elif not isinstance(username, str):
        errors.append('用户名必须是字符串')
    elif len(username.strip()) == 0:
        errors.append('用户名不能为空')
    elif len(username) > 50:
        errors.append('用户名长度不能超过50个字符')
    
    # 验证密码
    password = data.get('password')
    if not password:
        errors.append('密码不能为空')
    elif not isinstance(password, str):
        errors.append('密码必须是字符串')
    elif len(password.strip()) == 0:
        errors.append('密码不能为空')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }


def validate_pagination_params(page: Any, page_size: Any, max_page_size: int = 100) -> Dict[str, Any]:
    """验证分页参数"""
    errors = []
    validated_data = {}
    
    # 验证页码
    try:
        page = int(page) if page is not None else 1
        if page < 1:
            errors.append('页码必须大于0')
        else:
            validated_data['page'] = page
    except (ValueError, TypeError):
        errors.append('页码必须是有效的整数')
    
    # 验证页面大小
    try:
        page_size = int(page_size) if page_size is not None else 20
        if page_size < 1:
            errors.append('页面大小必须大于0')
        elif page_size > max_page_size:
            errors.append(f'页面大小不能超过{max_page_size}')
        else:
            validated_data['page_size'] = page_size
    except (ValueError, TypeError):
        errors.append('页面大小必须是有效的整数')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': validated_data
    }


def validate_date_range(start_date: str, end_date: str) -> Dict[str, Any]:
    """验证日期范围"""
    errors = []
    validated_data = {}
    
    # 验证开始日期
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            validated_data['start_date'] = start_dt
        except ValueError:
            errors.append('开始日期格式无效，请使用 YYYY-MM-DD 格式')
    
    # 验证结束日期
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            validated_data['end_date'] = end_dt
        except ValueError:
            errors.append('结束日期格式无效，请使用 YYYY-MM-DD 格式')
    
    # 验证日期范围逻辑
    if 'start_date' in validated_data and 'end_date' in validated_data:
        if validated_data['start_date'] > validated_data['end_date']:
            errors.append('开始日期不能晚于结束日期')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': validated_data
    }


def validate_customer_name(customer_name: str) -> Dict[str, Any]:
    """验证客户名称"""
    errors = []
    
    if not customer_name:
        errors.append('客户名称不能为空')
    elif not isinstance(customer_name, str):
        errors.append('客户名称必须是字符串')
    elif len(customer_name.strip()) == 0:
        errors.append('客户名称不能为空')
    elif len(customer_name) > 100:
        errors.append('客户名称长度不能超过100个字符')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': {'customer_name': customer_name.strip() if customer_name else ''}
    }


def validate_order_status(status: str) -> Dict[str, Any]:
    """验证订单状态"""
    valid_statuses = [
        '逾期还款', '提前还款', '按时还款', '账单日', 
        '逾期未还', '无效日期', '未到还款日期', '催收', '诉讼', '未知状态'
    ]
    
    errors = []
    
    if not status:
        errors.append('订单状态不能为空')
    elif status not in valid_statuses:
        errors.append(f'无效的订单状态，有效状态为: {", ".join(valid_statuses)}')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': {'status': status}
    }


def validate_export_format(format_type: str) -> Dict[str, Any]:
    """验证导出格式"""
    valid_formats = ['excel', 'pdf', 'csv']
    
    errors = []
    
    if not format_type:
        errors.append('导出格式不能为空')
    elif format_type.lower() not in valid_formats:
        errors.append(f'不支持的导出格式，支持的格式: {", ".join(valid_formats)}')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': {'format': format_type.lower()}
    }


def validate_search_query(query: str, min_length: int = 1, max_length: int = 100) -> Dict[str, Any]:
    """验证搜索查询"""
    errors = []
    
    if not query:
        errors.append('搜索关键词不能为空')
    elif not isinstance(query, str):
        errors.append('搜索关键词必须是字符串')
    elif len(query.strip()) < min_length:
        errors.append(f'搜索关键词长度不能少于{min_length}个字符')
    elif len(query) > max_length:
        errors.append(f'搜索关键词长度不能超过{max_length}个字符')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': {'query': query.strip() if query else ''}
    }


def validate_email(email: str) -> Dict[str, Any]:
    """验证邮箱地址"""
    errors = []
    
    if not email:
        errors.append('邮箱地址不能为空')
    elif not isinstance(email, str):
        errors.append('邮箱地址必须是字符串')
    else:
        # 简单的邮箱格式验证
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            errors.append('邮箱地址格式无效')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': {'email': email.strip().lower() if email else ''}
    }


def validate_phone_number(phone: str) -> Dict[str, Any]:
    """验证手机号码"""
    errors = []
    
    if not phone:
        errors.append('手机号码不能为空')
    elif not isinstance(phone, str):
        errors.append('手机号码必须是字符串')
    else:
        # 中国手机号码格式验证
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, phone):
            errors.append('手机号码格式无效')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'data': {'phone': phone.strip() if phone else ''}
    }


def sanitize_input(input_str: str) -> str:
    """清理输入字符串，防止XSS攻击"""
    if not isinstance(input_str, str):
        return str(input_str)
    
    # 移除潜在的危险字符
    dangerous_chars = ['<', '>', '"', "'", '&', 'javascript:', 'script']
    cleaned = input_str
    
    for char in dangerous_chars:
        cleaned = cleaned.replace(char, '')
    
    return cleaned.strip()
