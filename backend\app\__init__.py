"""
Flask API 后端应用初始化
现代化的 Flask 应用，提供 RESTful API 服务
"""

from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_caching import Cache
import logging
import os
from datetime import timedelta

# 导入配置
from backend.config.config import config_by_name

# 初始化扩展
cors = CORS()
jwt = JWTManager()
cache = Cache()

def create_app(config_name='development'):
    """创建 Flask 应用实例"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config_by_name[config_name])
    
    # 初始化扩展
    cors.init_app(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    jwt.init_app(app)
    cache.init_app(app)
    
    # JWT 配置
    app.config['JWT_SECRET_KEY'] = app.config.get('SECRET_KEY')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=8)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    
    # 配置日志
    setup_logging(app)
    
    # 注册 API 蓝图
    from backend.app.api.auth import auth_bp
    from backend.app.api.customers import customers_bp
    from backend.app.api.orders import orders_bp
    from backend.app.api.exports import exports_bp
    from backend.app.api.dashboard import dashboard_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(customers_bp, url_prefix='/api/customers')
    app.register_blueprint(orders_bp, url_prefix='/api/orders')
    app.register_blueprint(exports_bp, url_prefix='/api/exports')
    app.register_blueprint(dashboard_bp, url_prefix='/api/dashboard')
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # JWT 错误处理
    register_jwt_handlers(app)
    
    return app


def setup_logging(app):
    """配置日志系统"""
    if not app.debug:
        # 生产环境日志配置
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = logging.FileHandler('logs/api.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('Flask API 启动')


def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return {'error': '请求参数错误', 'message': str(error)}, 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return {'error': '未授权访问', 'message': '请先登录'}, 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return {'error': '访问被禁止', 'message': '权限不足'}, 403
    
    @app.errorhandler(404)
    def not_found(error):
        return {'error': '资源未找到', 'message': '请求的资源不存在'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {'error': '服务器内部错误', 'message': '请稍后重试'}, 500


def register_jwt_handlers(app):
    """注册 JWT 错误处理器"""
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {'error': 'Token 已过期', 'message': '请重新登录'}, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {'error': 'Token 无效', 'message': '请重新登录'}, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {'error': '缺少 Token', 'message': '请先登录'}, 401
