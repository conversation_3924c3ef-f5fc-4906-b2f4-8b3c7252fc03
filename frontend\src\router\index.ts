import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  easing: 'ease',
  speed: 500
})

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      layout: 'auth'
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '工作台',
      requiresAuth: true,
      icon: 'Dashboard',
      keepAlive: true
    }
  },
  {
    path: '/customers',
    name: 'Customers',
    component: () => import('@/views/customers/CustomerList.vue'),
    meta: {
      title: '客户管理',
      requiresAuth: true,
      icon: 'User',
      permissions: ['read']
    }
  },
  {
    path: '/customers/:id',
    name: 'CustomerDetail',
    component: () => import('@/views/customers/CustomerDetail.vue'),
    meta: {
      title: '客户详情',
      requiresAuth: true,
      hideInMenu: true,
      permissions: ['read']
    }
  },
  {
    path: '/customers/:id/summary',
    name: 'CustomerSummary',
    component: () => import('@/views/customers/CustomerSummary.vue'),
    meta: {
      title: '客户汇总',
      requiresAuth: true,
      hideInMenu: true,
      permissions: ['read'],
      keepAlive: true
    }
  },
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/views/orders/OrderList.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true,
      icon: 'Document',
      permissions: ['read']
    }
  },
  {
    path: '/orders/overdue',
    name: 'OverdueOrders',
    component: () => import('@/views/orders/OverdueOrders.vue'),
    meta: {
      title: '逾期订单',
      requiresAuth: true,
      icon: 'Warning',
      permissions: ['read']
    }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('@/views/reports/ReportList.vue'),
    meta: {
      title: '报表中心',
      requiresAuth: true,
      icon: 'DataAnalysis',
      permissions: ['read']
    }
  },
  {
    path: '/tools',
    name: 'Tools',
    component: () => import('@/views/tools/ToolList.vue'),
    meta: {
      title: '工具箱',
      requiresAuth: true,
      icon: 'Tools',
      permissions: ['write']
    }
  },
  {
    path: '/tools/contract-generator',
    name: 'ContractGenerator',
    component: () => import('@/views/tools/ContractGenerator.vue'),
    meta: {
      title: '合同生成器',
      requiresAuth: true,
      hideInMenu: true,
      permissions: ['write']
    }
  },
  {
    path: '/tools/receipt-generator',
    name: 'ReceiptGenerator',
    component: () => import('@/views/tools/ReceiptGenerator.vue'),
    meta: {
      title: '回执生成器',
      requiresAuth: true,
      hideInMenu: true,
      permissions: ['write']
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/settings/Settings.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true,
      icon: 'Setting',
      permissions: ['admin']
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/user/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面未找到',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '访问被禁止',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: '服务器错误',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // 设置页面标题
  const title = to.meta.title as string
  if (title) {
    document.title = `${title} - 太享查询系统`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    if (!userStore.isAuthenticated) {
      // 尝试从本地存储恢复登录状态
      const restored = await userStore.restoreAuthState()
      if (!restored) {
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
    
    // 检查权限
    const permissions = to.meta.permissions as string[]
    if (permissions && permissions.length > 0) {
      const hasPermission = permissions.some(permission => 
        userStore.hasPermission(permission)
      )
      
      if (!hasPermission) {
        next('/403')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  // 更新应用状态
  appStore.setCurrentRoute(to)
  
  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 记录路由变化（用于分析）
  if (import.meta.env.DEV) {
    console.log(`🧭 路由变化: ${from.path} → ${to.path}`)
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('🚨 路由错误:', error)
  NProgress.done()
})

export default router
