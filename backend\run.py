"""
Flask API 应用启动文件
现代化的 Flask + Vue.js 架构
"""

import os
import sys
from backend.app import create_app

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def main():
    """主函数"""
    # 获取环境配置
    config_name = os.getenv('FLASK_ENV', 'development')
    
    # 创建应用实例
    app = create_app(config_name)
    
    # 获取运行配置
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = config_name == 'development'
    
    print(f"🚀 启动 Flask API 服务")
    print(f"📍 环境: {config_name}")
    print(f"🌐 地址: http://{host}:{port}")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    
    # 启动应用
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )

if __name__ == '__main__':
    main()
