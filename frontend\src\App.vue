<template>
  <div id="app" class="app-container">
    <!-- 全局加载进度条 -->
    <div v-if="appStore.loading" class="global-loading">
      <el-loading-service />
    </div>

    <!-- 主应用内容 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="route.meta.transition || 'fade'"
        mode="out-in"
        appear
      >
        <keep-alive :include="keepAliveComponents">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </transition>
    </router-view>

    <!-- 全局通知容器 -->
    <el-backtop :right="40" :bottom="40" />
    
    <!-- 全局错误边界 -->
    <ErrorBoundary />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

// 使用 stores
const appStore = useAppStore()
const userStore = useUserStore()
const themeStore = useThemeStore()

// 需要缓存的组件
const keepAliveComponents = computed(() => [
  'Dashboard',
  'CustomerSummary',
  'OrderManagement'
])

// 应用初始化
onMounted(async () => {
  try {
    // 初始化主题
    themeStore.initTheme()
    
    // 检查用户登录状态
    await userStore.checkAuthStatus()
    
    // 初始化应用配置
    await appStore.initializeApp()
    
    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
  }
})

// 监听窗口大小变化
const handleResize = () => {
  appStore.updateWindowSize({
    width: window.innerWidth,
    height: window.innerHeight
  })
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化窗口尺寸
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听网络状态
const handleOnline = () => appStore.setNetworkStatus(true)
const handleOffline = () => appStore.setNetworkStatus(false)

onMounted(() => {
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  appStore.setNetworkStatus(navigator.onLine)
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<style lang="scss">
// 全局样式
.app-container {
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  transition: all 0.3s ease;
}

// 全局加载样式
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(30px);
  opacity: 0;
}

// 响应式断点
@media (max-width: 768px) {
  .app-container {
    padding: 0;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-fill-color-darker);
  }
}

// 打印样式
@media print {
  .app-container {
    background: white !important;
    color: black !important;
  }
  
  .no-print {
    display: none !important;
  }
}
</style>
