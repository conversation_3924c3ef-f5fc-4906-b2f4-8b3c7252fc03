/**
 * 认证服务
 * 处理用户登录、登出、Token 管理等功能
 */

import { http } from '@/utils/http'
import type { ApiResponse } from '@/types/app'
import type { LoginCredentials, LoginResponse, User } from '@/types/user'

export const authApi = {
  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> {
    return await http.post('/auth/login', credentials)
  },

  /**
   * 用户登出
   */
  async logout(): Promise<ApiResponse<null>> {
    return await http.post('/auth/logout')
  },

  /**
   * 刷新访问令牌
   */
  async refreshToken(): Promise<ApiResponse<{ access_token: string }>> {
    return await http.post('/auth/refresh')
  },

  /**
   * 检查 Token 有效性
   */
  async checkToken(): Promise<ApiResponse<{
    valid: boolean
    username: string
    expires_at: number
    user_level: string
  }>> {
    return await http.get('/auth/check-token')
  },

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return await http.get('/auth/me')
  },

  /**
   * 修改密码
   */
  async changePassword(data: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse<null>> {
    return await http.post('/auth/change-password', data)
  },

  /**
   * 重置密码（管理员功能）
   */
  async resetPassword(username: string, newPassword: string): Promise<ApiResponse<null>> {
    return await http.post('/auth/reset-password', {
      username,
      newPassword
    })
  },

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(username?: string): Promise<ApiResponse<string[]>> {
    const url = username ? `/auth/permissions/${username}` : '/auth/permissions'
    return await http.get(url)
  },

  /**
   * 验证用户权限
   */
  async validatePermission(permission: string): Promise<ApiResponse<{
    hasPermission: boolean
    userLevel: string
  }>> {
    return await http.post('/auth/validate-permission', { permission })
  }
}
