/**
 * 本地存储工具
 * 提供统一的本地存储管理功能
 */

import type { User } from '@/types/user'

// 存储键名常量
const STORAGE_KEYS = {
  TOKEN: 'hdsc_access_token',
  REFRESH_TOKEN: 'hdsc_refresh_token',
  USER: 'hdsc_user_info',
  PREFERENCES: 'hdsc_user_preferences',
  THEME: 'hdsc_theme',
  LANGUAGE: 'hdsc_language',
  SIDEBAR_COLLAPSED: 'hdsc_sidebar_collapsed',
  RECENT_SEARCHES: 'hdsc_recent_searches',
  FAVORITES: 'hdsc_favorites',
  CACHE_PREFIX: 'hdsc_cache_'
} as const

/**
 * 安全的 JSON 解析
 */
function safeJsonParse<T>(value: string | null, defaultValue: T): T {
  if (!value) return defaultValue
  
  try {
    return JSON.parse(value)
  } catch (error) {
    console.warn('JSON 解析失败:', error)
    return defaultValue
  }
}

/**
 * 安全的 JSON 字符串化
 */
function safeJsonStringify(value: any): string {
  try {
    return JSON.stringify(value)
  } catch (error) {
    console.warn('JSON 字符串化失败:', error)
    return ''
  }
}

/**
 * 检查 localStorage 是否可用
 */
function isLocalStorageAvailable(): boolean {
  try {
    const test = '__localStorage_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch {
    return false
  }
}

/**
 * 检查 sessionStorage 是否可用
 */
function isSessionStorageAvailable(): boolean {
  try {
    const test = '__sessionStorage_test__'
    sessionStorage.setItem(test, test)
    sessionStorage.removeItem(test)
    return true
  } catch {
    return false
  }
}

// 存储适配器
class StorageAdapter {
  private useLocalStorage: boolean
  private useSessionStorage: boolean

  constructor() {
    this.useLocalStorage = isLocalStorageAvailable()
    this.useSessionStorage = isSessionStorageAvailable()
  }

  setItem(key: string, value: string, persistent = true): void {
    try {
      if (persistent && this.useLocalStorage) {
        localStorage.setItem(key, value)
      } else if (this.useSessionStorage) {
        sessionStorage.setItem(key, value)
      } else {
        // 降级到内存存储
        this.memoryStorage.set(key, value)
      }
    } catch (error) {
      console.warn('存储失败:', error)
    }
  }

  getItem(key: string): string | null {
    try {
      if (this.useLocalStorage) {
        const value = localStorage.getItem(key)
        if (value !== null) return value
      }
      
      if (this.useSessionStorage) {
        const value = sessionStorage.getItem(key)
        if (value !== null) return value
      }
      
      // 从内存存储获取
      return this.memoryStorage.get(key) || null
    } catch (error) {
      console.warn('读取存储失败:', error)
      return null
    }
  }

  removeItem(key: string): void {
    try {
      if (this.useLocalStorage) {
        localStorage.removeItem(key)
      }
      if (this.useSessionStorage) {
        sessionStorage.removeItem(key)
      }
      this.memoryStorage.delete(key)
    } catch (error) {
      console.warn('删除存储失败:', error)
    }
  }

  clear(): void {
    try {
      if (this.useLocalStorage) {
        // 只清除应用相关的键
        Object.values(STORAGE_KEYS).forEach(key => {
          localStorage.removeItem(key)
        })
      }
      if (this.useSessionStorage) {
        sessionStorage.clear()
      }
      this.memoryStorage.clear()
    } catch (error) {
      console.warn('清除存储失败:', error)
    }
  }

  // 内存存储作为降级方案
  private memoryStorage = new Map<string, string>()
}

// 创建存储实例
const storageAdapter = new StorageAdapter()

// 导出存储工具
export const storage = {
  // Token 相关
  setToken(token: string): void {
    storageAdapter.setItem(STORAGE_KEYS.TOKEN, token, true)
  },

  getToken(): string | null {
    return storageAdapter.getItem(STORAGE_KEYS.TOKEN)
  },

  removeToken(): void {
    storageAdapter.removeItem(STORAGE_KEYS.TOKEN)
  },

  setRefreshToken(token: string): void {
    storageAdapter.setItem(STORAGE_KEYS.REFRESH_TOKEN, token, true)
  },

  getRefreshToken(): string | null {
    return storageAdapter.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  },

  removeRefreshToken(): void {
    storageAdapter.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
  },

  // 用户信息
  setUser(user: User): void {
    storageAdapter.setItem(STORAGE_KEYS.USER, safeJsonStringify(user), true)
  },

  getUser(): User | null {
    const userStr = storageAdapter.getItem(STORAGE_KEYS.USER)
    return safeJsonParse(userStr, null)
  },

  removeUser(): void {
    storageAdapter.removeItem(STORAGE_KEYS.USER)
  },

  // 用户偏好设置
  setPreferences(preferences: Record<string, any>): void {
    storageAdapter.setItem(STORAGE_KEYS.PREFERENCES, safeJsonStringify(preferences), true)
  },

  getPreferences(): Record<string, any> {
    const preferencesStr = storageAdapter.getItem(STORAGE_KEYS.PREFERENCES)
    return safeJsonParse(preferencesStr, {})
  },

  // 主题设置
  setTheme(theme: string): void {
    storageAdapter.setItem(STORAGE_KEYS.THEME, theme, true)
  },

  getTheme(): string | null {
    return storageAdapter.getItem(STORAGE_KEYS.THEME)
  },

  // 语言设置
  setLanguage(language: string): void {
    storageAdapter.setItem(STORAGE_KEYS.LANGUAGE, language, true)
  },

  getLanguage(): string | null {
    return storageAdapter.getItem(STORAGE_KEYS.LANGUAGE)
  },

  // 侧边栏状态
  setSidebarCollapsed(collapsed: boolean): void {
    storageAdapter.setItem(STORAGE_KEYS.SIDEBAR_COLLAPSED, String(collapsed), true)
  },

  getSidebarCollapsed(): boolean {
    const collapsed = storageAdapter.getItem(STORAGE_KEYS.SIDEBAR_COLLAPSED)
    return collapsed === 'true'
  },

  // 最近搜索
  setRecentSearches(searches: string[]): void {
    const limitedSearches = searches.slice(0, 10) // 限制最多10条
    storageAdapter.setItem(STORAGE_KEYS.RECENT_SEARCHES, safeJsonStringify(limitedSearches), true)
  },

  getRecentSearches(): string[] {
    const searchesStr = storageAdapter.getItem(STORAGE_KEYS.RECENT_SEARCHES)
    return safeJsonParse(searchesStr, [])
  },

  addRecentSearch(search: string): void {
    const searches = this.getRecentSearches()
    const filteredSearches = searches.filter(s => s !== search)
    filteredSearches.unshift(search)
    this.setRecentSearches(filteredSearches)
  },

  // 收藏夹
  setFavorites(favorites: string[]): void {
    storageAdapter.setItem(STORAGE_KEYS.FAVORITES, safeJsonStringify(favorites), true)
  },

  getFavorites(): string[] {
    const favoritesStr = storageAdapter.getItem(STORAGE_KEYS.FAVORITES)
    return safeJsonParse(favoritesStr, [])
  },

  addFavorite(item: string): void {
    const favorites = this.getFavorites()
    if (!favorites.includes(item)) {
      favorites.push(item)
      this.setFavorites(favorites)
    }
  },

  removeFavorite(item: string): void {
    const favorites = this.getFavorites()
    const filteredFavorites = favorites.filter(f => f !== item)
    this.setFavorites(filteredFavorites)
  },

  // 缓存管理
  setCache(key: string, data: any, ttl?: number): void {
    const cacheKey = STORAGE_KEYS.CACHE_PREFIX + key
    const cacheData = {
      data,
      timestamp: Date.now(),
      ttl: ttl || 0
    }
    storageAdapter.setItem(cacheKey, safeJsonStringify(cacheData), false)
  },

  getCache<T>(key: string): T | null {
    const cacheKey = STORAGE_KEYS.CACHE_PREFIX + key
    const cacheStr = storageAdapter.getItem(cacheKey)
    const cacheData = safeJsonParse(cacheStr, null)
    
    if (!cacheData) return null
    
    // 检查是否过期
    if (cacheData.ttl > 0 && Date.now() - cacheData.timestamp > cacheData.ttl) {
      this.removeCache(key)
      return null
    }
    
    return cacheData.data
  },

  removeCache(key: string): void {
    const cacheKey = STORAGE_KEYS.CACHE_PREFIX + key
    storageAdapter.removeItem(cacheKey)
  },

  // 清除所有数据
  clear(): void {
    storageAdapter.clear()
  },

  // 清除认证相关数据
  clearAuth(): void {
    this.removeToken()
    this.removeRefreshToken()
    this.removeUser()
  }
}
