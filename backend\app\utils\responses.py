"""
API 响应工具
提供统一的 API 响应格式
"""

from typing import Any, Dict, Optional, List
from datetime import datetime


def success_response(
    data: Any = None, 
    message: str = "操作成功", 
    code: str = "SUCCESS",
    meta: Optional[Dict] = None
) -> Dict[str, Any]:
    """成功响应格式"""
    response = {
        "success": True,
        "code": code,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "data": data
    }
    
    if meta:
        response["meta"] = meta
    
    return response


def error_response(
    message: str = "操作失败",
    code: str = "ERROR",
    details: Optional[Any] = None,
    errors: Optional[List[str]] = None
) -> Dict[str, Any]:
    """错误响应格式"""
    response = {
        "success": False,
        "code": code,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "data": None
    }
    
    if details:
        response["details"] = details
    
    if errors:
        response["errors"] = errors
    
    return response


def paginated_response(
    data: List[Any],
    page: int,
    page_size: int,
    total: int,
    message: str = "获取数据成功"
) -> Dict[str, Any]:
    """分页响应格式"""
    total_pages = (total + page_size - 1) // page_size
    
    meta = {
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total": total,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }
    }
    
    return success_response(
        data=data,
        message=message,
        meta=meta
    )


def validation_error_response(errors: List[str]) -> Dict[str, Any]:
    """验证错误响应"""
    return error_response(
        message="数据验证失败",
        code="VALIDATION_ERROR",
        errors=errors
    )


def not_found_response(resource: str = "资源") -> Dict[str, Any]:
    """资源未找到响应"""
    return error_response(
        message=f"{resource}未找到",
        code="NOT_FOUND"
    )


def unauthorized_response(message: str = "未授权访问") -> Dict[str, Any]:
    """未授权响应"""
    return error_response(
        message=message,
        code="UNAUTHORIZED"
    )


def forbidden_response(message: str = "权限不足") -> Dict[str, Any]:
    """禁止访问响应"""
    return error_response(
        message=message,
        code="FORBIDDEN"
    )


def internal_error_response(message: str = "服务器内部错误") -> Dict[str, Any]:
    """服务器内部错误响应"""
    return error_response(
        message=message,
        code="INTERNAL_ERROR"
    )


def rate_limit_response(message: str = "请求过于频繁") -> Dict[str, Any]:
    """限流响应"""
    return error_response(
        message=message,
        code="RATE_LIMIT_EXCEEDED"
    )


def maintenance_response(message: str = "系统维护中") -> Dict[str, Any]:
    """系统维护响应"""
    return error_response(
        message=message,
        code="MAINTENANCE"
    )


class ResponseBuilder:
    """响应构建器类"""
    
    def __init__(self):
        self.response_data = {
            "success": True,
            "code": "SUCCESS",
            "message": "操作成功",
            "timestamp": datetime.now().isoformat(),
            "data": None
        }
    
    def success(self, success: bool = True):
        """设置成功状态"""
        self.response_data["success"] = success
        return self
    
    def code(self, code: str):
        """设置响应代码"""
        self.response_data["code"] = code
        return self
    
    def message(self, message: str):
        """设置响应消息"""
        self.response_data["message"] = message
        return self
    
    def data(self, data: Any):
        """设置响应数据"""
        self.response_data["data"] = data
        return self
    
    def meta(self, meta: Dict):
        """设置元数据"""
        self.response_data["meta"] = meta
        return self
    
    def errors(self, errors: List[str]):
        """设置错误列表"""
        self.response_data["errors"] = errors
        return self
    
    def details(self, details: Any):
        """设置详细信息"""
        self.response_data["details"] = details
        return self
    
    def build(self) -> Dict[str, Any]:
        """构建响应"""
        return self.response_data.copy()


# 常用响应代码常量
class ResponseCodes:
    """响应代码常量"""
    
    # 成功响应
    SUCCESS = "SUCCESS"
    CREATED = "CREATED"
    UPDATED = "UPDATED"
    DELETED = "DELETED"
    
    # 客户端错误
    BAD_REQUEST = "BAD_REQUEST"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    DUPLICATE_ERROR = "DUPLICATE_ERROR"
    
    # 服务器错误
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    TIMEOUT = "TIMEOUT"
    
    # 业务错误
    BUSINESS_ERROR = "BUSINESS_ERROR"
    DATA_ERROR = "DATA_ERROR"
    PERMISSION_ERROR = "PERMISSION_ERROR"
    
    # 认证错误
    LOGIN_ERROR = "LOGIN_ERROR"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    TOKEN_INVALID = "TOKEN_INVALID"
    
    # 限流和维护
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    MAINTENANCE = "MAINTENANCE"
