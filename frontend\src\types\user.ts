/**
 * 用户相关类型定义
 */

// 用户权限类型
export type UserPermission = 'read' | 'write' | 'admin'

// 用户级别类型
export type UserLevel = 'limited' | 'standard' | 'full'

// 用户接口
export interface User {
  username: string
  level: UserLevel
  permissions: UserPermission[]
  last_login?: string
  created_at?: string
  avatar?: string
  email?: string
  phone?: string
  department?: string
  position?: string
}

// 登录凭据
export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

// 登录响应
export interface LoginResponse {
  access_token: string
  refresh_token: string
  user: User
}

// 用户配置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  pageSize: number
  sidebarCollapsed: boolean
  notifications: {
    email: boolean
    browser: boolean
    sound: boolean
  }
  dashboard: {
    layout: 'grid' | 'list'
    widgets: string[]
  }
}

// 用户统计信息
export interface UserStats {
  loginCount: number
  lastLoginTime: string
  totalQueries: number
  totalExports: number
  favoritePages: string[]
  recentActivities: UserActivity[]
}

// 用户活动记录
export interface UserActivity {
  id: string
  type: 'login' | 'query' | 'export' | 'view' | 'create' | 'update' | 'delete'
  description: string
  timestamp: string
  ip?: string
  userAgent?: string
  details?: Record<string, any>
}

// 用户会话信息
export interface UserSession {
  sessionId: string
  userId: string
  loginTime: string
  lastActivity: string
  ip: string
  userAgent: string
  isActive: boolean
  expiresAt: string
}

// 密码修改请求
export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 用户资料更新请求
export interface UpdateProfileRequest {
  email?: string
  phone?: string
  department?: string
  position?: string
  avatar?: string
}

// 权限检查结果
export interface PermissionCheck {
  hasPermission: boolean
  requiredLevel: UserLevel
  currentLevel: UserLevel
  missingPermissions: UserPermission[]
}

// 用户搜索条件
export interface UserSearchCriteria {
  keyword?: string
  level?: UserLevel
  permissions?: UserPermission[]
  department?: string
  isActive?: boolean
  createdAfter?: string
  createdBefore?: string
  lastLoginAfter?: string
  lastLoginBefore?: string
}

// 用户列表项
export interface UserListItem {
  username: string
  level: UserLevel
  permissions: UserPermission[]
  department?: string
  position?: string
  isActive: boolean
  lastLogin?: string
  createdAt: string
}

// 用户创建请求
export interface CreateUserRequest {
  username: string
  password: string
  level: UserLevel
  email?: string
  phone?: string
  department?: string
  position?: string
}

// 用户更新请求
export interface UpdateUserRequest {
  level?: UserLevel
  permissions?: UserPermission[]
  email?: string
  phone?: string
  department?: string
  position?: string
  isActive?: boolean
}

// 批量用户操作
export interface BatchUserOperation {
  usernames: string[]
  operation: 'activate' | 'deactivate' | 'delete' | 'updateLevel' | 'updatePermissions'
  params?: {
    level?: UserLevel
    permissions?: UserPermission[]
  }
}

// 用户导出选项
export interface UserExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  fields: string[]
  filters?: UserSearchCriteria
  includeStats?: boolean
  includeActivities?: boolean
}
