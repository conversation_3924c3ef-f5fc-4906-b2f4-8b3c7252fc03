# 生产环境配置

# 应用标题
VITE_APP_TITLE=太享查询系统

# API 基础地址 (生产环境需要修改为实际地址)
VITE_API_BASE_URL=/api

# 应用版本
VITE_APP_VERSION=2.0.0

# 环境标识
VITE_NODE_ENV=production

# 是否启用 Mock 数据
VITE_USE_MOCK=false

# 是否启用调试模式
VITE_DEBUG=false

# 上传文件大小限制 (MB)
VITE_UPLOAD_SIZE_LIMIT=10

# 分页默认大小
VITE_PAGE_SIZE=20

# 最大分页大小
VITE_MAX_PAGE_SIZE=100

# 缓存过期时间 (分钟)
VITE_CACHE_TIMEOUT=30

# 是否启用 PWA
VITE_PWA_ENABLED=true

# 是否启用性能监控
VITE_PERFORMANCE_MONITOR=false
