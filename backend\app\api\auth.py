"""
认证 API 路由
处理用户登录、登出、Token 刷新等认证相关功能
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import (
    create_access_token, create_refresh_token, 
    jwt_required, get_jwt_identity, get_jwt
)
from werkzeug.security import check_password_hash
import logging

from backend.app.services.auth_service import AuthService
from backend.app.utils.validators import validate_login_data
from backend.app.utils.responses import success_response, error_response

logger = logging.getLogger(__name__)

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

# 初始化认证服务
auth_service = AuthService()

# 用于存储已撤销的 token（生产环境应使用 Redis）
revoked_tokens = set()


@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        # 获取请求数据
        data = request.get_json()
        
        # 验证请求数据
        validation_result = validate_login_data(data)
        if not validation_result['valid']:
            return error_response(
                message='请求数据无效',
                details=validation_result['errors']
            ), 400
        
        username = data.get('username')
        password = data.get('password')
        
        # 验证用户凭据
        user = auth_service.authenticate_user(username, password)
        if not user:
            return error_response(
                message='用户名或密码错误',
                code='INVALID_CREDENTIALS'
            ), 401
        
        # 创建 JWT tokens
        access_token = create_access_token(
            identity=username,
            additional_claims={
                'user_level': user.get('level', 'limited'),
                'permissions': user.get('permissions', [])
            }
        )
        refresh_token = create_refresh_token(identity=username)
        
        logger.info(f'用户 {username} 登录成功')
        
        return success_response(
            data={
                'access_token': access_token,
                'refresh_token': refresh_token,
                'user': {
                    'username': username,
                    'level': user.get('level', 'limited'),
                    'permissions': user.get('permissions', [])
                }
            },
            message='登录成功'
        )
        
    except Exception as e:
        logger.error(f'登录失败: {str(e)}')
        return error_response(
            message='登录失败，请稍后重试',
            code='LOGIN_ERROR'
        ), 500


@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        # 获取当前 token
        jti = get_jwt()['jti']  # JWT ID
        username = get_jwt_identity()
        
        # 将 token 添加到撤销列表
        revoked_tokens.add(jti)
        
        logger.info(f'用户 {username} 登出成功')
        
        return success_response(message='登出成功')
        
    except Exception as e:
        logger.error(f'登出失败: {str(e)}')
        return error_response(
            message='登出失败，请稍后重试',
            code='LOGOUT_ERROR'
        ), 500


@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        current_user = get_jwt_identity()
        
        # 获取用户信息
        user = auth_service.get_user_info(current_user)
        if not user:
            return error_response(
                message='用户不存在',
                code='USER_NOT_FOUND'
            ), 404
        
        # 创建新的访问令牌
        new_access_token = create_access_token(
            identity=current_user,
            additional_claims={
                'user_level': user.get('level', 'limited'),
                'permissions': user.get('permissions', [])
            }
        )
        
        return success_response(
            data={'access_token': new_access_token},
            message='Token 刷新成功'
        )
        
    except Exception as e:
        logger.error(f'Token 刷新失败: {str(e)}')
        return error_response(
            message='Token 刷新失败',
            code='REFRESH_ERROR'
        ), 500


@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """获取当前用户信息"""
    try:
        current_user = get_jwt_identity()
        
        # 获取用户详细信息
        user = auth_service.get_user_info(current_user)
        if not user:
            return error_response(
                message='用户不存在',
                code='USER_NOT_FOUND'
            ), 404
        
        return success_response(
            data={
                'username': current_user,
                'level': user.get('level', 'limited'),
                'permissions': user.get('permissions', []),
                'last_login': user.get('last_login'),
                'created_at': user.get('created_at')
            },
            message='获取用户信息成功'
        )
        
    except Exception as e:
        logger.error(f'获取用户信息失败: {str(e)}')
        return error_response(
            message='获取用户信息失败',
            code='GET_USER_ERROR'
        ), 500


@auth_bp.route('/check-token', methods=['GET'])
@jwt_required()
def check_token():
    """检查 Token 有效性"""
    try:
        current_user = get_jwt_identity()
        jwt_claims = get_jwt()
        
        return success_response(
            data={
                'valid': True,
                'username': current_user,
                'expires_at': jwt_claims.get('exp'),
                'user_level': jwt_claims.get('user_level', 'limited')
            },
            message='Token 有效'
        )
        
    except Exception as e:
        logger.error(f'Token 检查失败: {str(e)}')
        return error_response(
            message='Token 检查失败',
            code='TOKEN_CHECK_ERROR'
        ), 500


# JWT 回调函数：检查 token 是否被撤销
@auth_bp.before_app_request
def check_if_token_revoked():
    """检查 token 是否已被撤销"""
    pass  # 这里可以实现更复杂的 token 撤销逻辑
