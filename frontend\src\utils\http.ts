/**
 * HTTP 请求工具
 * 基于 Axios 封装，提供统一的请求/响应处理
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { storage } from './storage'
import router from '@/router'
import type { ApiResponse } from '@/types/app'

// 创建 Axios 实例
const instance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 添加认证 Token
    const token = storage.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log('🚀 HTTP Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        params: config.params,
        data: config.data
      })
    }

    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log('✅ HTTP Response:', {
        url: response.config.url,
        status: response.status,
        data: data
      })
    }

    // 检查业务状态码
    if (data.success === false) {
      // 处理业务错误
      handleBusinessError(data)
      return Promise.reject(new Error(data.message || '请求失败'))
    }

    return data
  },
  async (error) => {
    console.error('❌ Response Error:', error)

    // 网络错误
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = error.response

    // 处理 HTTP 状态码错误
    switch (status) {
      case 401:
        await handleUnauthorized()
        break
      case 403:
        ElMessage.error('权限不足，无法访问该资源')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 422:
        handleValidationError(data)
        break
      case 429:
        ElMessage.error('请求过于频繁，请稍后重试')
        break
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务暂时不可用，请稍后重试')
        break
      default:
        ElMessage.error(data?.message || `请求失败 (${status})`)
    }

    return Promise.reject(error)
  }
)

// 处理业务错误
function handleBusinessError(data: ApiResponse) {
  const { code, message, errors } = data

  switch (code) {
    case 'VALIDATION_ERROR':
      if (errors && errors.length > 0) {
        ElMessage.error(errors.join('; '))
      } else {
        ElMessage.error(message || '数据验证失败')
      }
      break
    case 'UNAUTHORIZED':
    case 'TOKEN_EXPIRED':
    case 'TOKEN_INVALID':
      handleUnauthorized()
      break
    case 'FORBIDDEN':
    case 'PERMISSION_ERROR':
      ElMessage.error('权限不足，无法执行该操作')
      break
    case 'NOT_FOUND':
      ElMessage.error('请求的资源不存在')
      break
    case 'RATE_LIMIT_EXCEEDED':
      ElMessage.error('操作过于频繁，请稍后重试')
      break
    case 'MAINTENANCE':
      ElMessage.error('系统维护中，请稍后访问')
      break
    default:
      ElMessage.error(message || '操作失败')
  }
}

// 处理验证错误
function handleValidationError(data: any) {
  if (data?.errors && Array.isArray(data.errors)) {
    const errorMessage = data.errors.join('; ')
    ElMessage.error(errorMessage)
  } else {
    ElMessage.error(data?.message || '数据验证失败')
  }
}

// 处理未授权错误
async function handleUnauthorized() {
  // 清除本地存储的认证信息
  storage.removeToken()
  storage.removeRefreshToken()
  storage.removeUser()

  // 显示登录提示
  try {
    await ElMessageBox.confirm(
      '登录状态已过期，请重新登录',
      '提示',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 跳转到登录页
    router.push({
      path: '/login',
      query: { redirect: router.currentRoute.value.fullPath }
    })
  } catch {
    // 用户取消，直接跳转到登录页
    router.push('/login')
  }
}

// 封装常用的 HTTP 方法
export const http = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.get(url, config)
  },

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.post(url, data, config)
  },

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.put(url, data, config)
  },

  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.patch(url, data, config)
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.delete(url, config)
  },

  upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    })
  },

  download(url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> {
    return instance.get(url, {
      ...config,
      responseType: 'blob'
    }).then((response: any) => {
      const blob = new Blob([response])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 导出 Axios 实例（用于特殊情况）
export { instance as axiosInstance }

export default http
