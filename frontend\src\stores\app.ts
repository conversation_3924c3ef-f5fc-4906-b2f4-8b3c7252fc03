import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'
import type { AppConfig, WindowSize, BreadcrumbItem } from '@/types/app'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const initialized = ref(false)
  const sidebarCollapsed = ref(false)
  const currentRoute = ref<RouteLocationNormalized | null>(null)
  const windowSize = ref<WindowSize>({
    width: window.innerWidth,
    height: window.innerHeight
  })
  const networkStatus = ref(navigator.onLine)
  const config = ref<AppConfig>({
    title: '太享查询系统',
    version: '2.0.0',
    apiBaseUrl: '/api',
    pageSize: 20,
    maxPageSize: 100,
    theme: 'light',
    language: 'zh-CN'
  })

  // 计算属性
  const isMobile = computed(() => windowSize.value.width < 768)
  
  const isTablet = computed(() => 
    windowSize.value.width >= 768 && windowSize.value.width < 1024
  )
  
  const isDesktop = computed(() => windowSize.value.width >= 1024)
  
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })
  
  const breadcrumbs = computed((): BreadcrumbItem[] => {
    if (!currentRoute.value) return []
    
    const route = currentRoute.value
    const breadcrumbs: BreadcrumbItem[] = []
    
    // 添加首页
    breadcrumbs.push({
      title: '首页',
      path: '/dashboard',
      icon: 'House'
    })
    
    // 根据路由生成面包屑
    if (route.path !== '/dashboard') {
      const pathSegments = route.path.split('/').filter(Boolean)
      let currentPath = ''
      
      pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`
        
        // 跳过参数段
        if (segment.startsWith(':') || /^\d+$/.test(segment)) {
          return
        }
        
        const isLast = index === pathSegments.length - 1
        const title = route.meta?.title as string || segment
        
        breadcrumbs.push({
          title,
          path: isLast ? route.path : currentPath,
          icon: route.meta?.icon as string
        })
      })
    }
    
    return breadcrumbs
  })

  // 方法
  const setLoading = (value: boolean): void => {
    loading.value = value
  }

  const toggleSidebar = (): void => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    
    // 在移动端自动收起侧边栏
    if (isMobile.value) {
      sidebarCollapsed.value = true
    }
  }

  const setSidebarCollapsed = (collapsed: boolean): void => {
    sidebarCollapsed.value = collapsed
  }

  const setCurrentRoute = (route: RouteLocationNormalized): void => {
    currentRoute.value = route
  }

  const updateWindowSize = (size: WindowSize): void => {
    windowSize.value = size
    
    // 在移动端自动收起侧边栏
    if (size.width < 768 && !sidebarCollapsed.value) {
      sidebarCollapsed.value = true
    }
  }

  const setNetworkStatus = (online: boolean): void => {
    networkStatus.value = online
  }

  const updateConfig = (newConfig: Partial<AppConfig>): void => {
    config.value = { ...config.value, ...newConfig }
  }

  const initializeApp = async (): Promise<void> => {
    try {
      setLoading(true)
      
      // 模拟初始化过程
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 设置初始侧边栏状态
      if (isMobile.value) {
        sidebarCollapsed.value = true
      }
      
      initialized.value = true
      
      console.log('✅ 应用初始化完成')
    } catch (error) {
      console.error('❌ 应用初始化失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const resetApp = (): void => {
    loading.value = false
    initialized.value = false
    sidebarCollapsed.value = false
    currentRoute.value = null
    
    // 重置配置为默认值
    config.value = {
      title: '太享查询系统',
      version: '2.0.0',
      apiBaseUrl: '/api',
      pageSize: 20,
      maxPageSize: 100,
      theme: 'light',
      language: 'zh-CN'
    }
  }

  const getPageTitle = (routeTitle?: string): string => {
    const baseTitle = config.value.title
    return routeTitle ? `${routeTitle} - ${baseTitle}` : baseTitle
  }

  const showGlobalLoading = (): void => {
    setLoading(true)
  }

  const hideGlobalLoading = (): void => {
    setLoading(false)
  }

  // 返回 store
  return {
    // 状态
    loading: readonly(loading),
    initialized: readonly(initialized),
    sidebarCollapsed: readonly(sidebarCollapsed),
    currentRoute: readonly(currentRoute),
    windowSize: readonly(windowSize),
    networkStatus: readonly(networkStatus),
    config: readonly(config),
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    breadcrumbs,
    
    // 方法
    setLoading,
    toggleSidebar,
    setSidebarCollapsed,
    setCurrentRoute,
    updateWindowSize,
    setNetworkStatus,
    updateConfig,
    initializeApp,
    resetApp,
    getPageTitle,
    showGlobalLoading,
    hideGlobalLoading
  }
})
