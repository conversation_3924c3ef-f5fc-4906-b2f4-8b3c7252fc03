{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@views/*": ["./src/views/*"], "@stores/*": ["./src/stores/*"], "@services/*": ["./src/services/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"], "@assets/*": ["./src/assets/*"]}, "types": ["vite/client", "element-plus/global"], "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true}}