import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginCredentials, UserPermission } from '@/types/user'
import { authApi } from '@/services/auth'
import { storage } from '@/utils/storage'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const loading = ref(false)
  const loginTime = ref<Date | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  
  const userLevel = computed(() => user.value?.level || 'limited')
  
  const permissions = computed(() => user.value?.permissions || [])
  
  const isAdmin = computed(() => permissions.value.includes('admin'))
  
  const canWrite = computed(() => permissions.value.includes('write'))
  
  const canRead = computed(() => permissions.value.includes('read'))

  // 方法
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      loading.value = true
      
      const response = await authApi.login(credentials)
      
      if (response.success) {
        // 保存用户信息和令牌
        user.value = response.data.user
        token.value = response.data.access_token
        refreshToken.value = response.data.refresh_token
        loginTime.value = new Date()
        
        // 持久化存储
        storage.setToken(token.value)
        storage.setRefreshToken(refreshToken.value)
        storage.setUser(user.value)
        
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error: any) {
      console.error('登录错误:', error)
      ElMessage.error(error.message || '登录失败，请稍后重试')
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      loading.value = true
      
      // 调用后端登出接口
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      // 清除本地状态
      clearUserData()
      loading.value = false
      ElMessage.success('已安全退出')
    }
  }

  const refreshAccessToken = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) {
        return false
      }

      const response = await authApi.refreshToken()
      
      if (response.success) {
        token.value = response.data.access_token
        storage.setToken(token.value)
        return true
      } else {
        // 刷新失败，清除用户数据
        clearUserData()
        return false
      }
    } catch (error) {
      console.error('刷新令牌错误:', error)
      clearUserData()
      return false
    }
  }

  const checkAuthStatus = async (): Promise<boolean> => {
    try {
      if (!token.value) {
        return false
      }

      const response = await authApi.checkToken()
      
      if (response.success) {
        // 更新用户信息
        if (!user.value) {
          const userResponse = await authApi.getCurrentUser()
          if (userResponse.success) {
            user.value = userResponse.data
            storage.setUser(user.value)
          }
        }
        return true
      } else {
        // 令牌无效，尝试刷新
        return await refreshAccessToken()
      }
    } catch (error) {
      console.error('检查认证状态错误:', error)
      return false
    }
  }

  const restoreAuthState = async (): Promise<boolean> => {
    try {
      // 从本地存储恢复状态
      const storedToken = storage.getToken()
      const storedRefreshToken = storage.getRefreshToken()
      const storedUser = storage.getUser()

      if (!storedToken || !storedUser) {
        return false
      }

      // 恢复状态
      token.value = storedToken
      refreshToken.value = storedRefreshToken || ''
      user.value = storedUser

      // 验证令牌有效性
      return await checkAuthStatus()
    } catch (error) {
      console.error('恢复认证状态错误:', error)
      clearUserData()
      return false
    }
  }

  const updateUserInfo = async (): Promise<void> => {
    try {
      const response = await authApi.getCurrentUser()
      
      if (response.success) {
        user.value = response.data
        storage.setUser(user.value)
      }
    } catch (error) {
      console.error('更新用户信息错误:', error)
    }
  }

  const hasPermission = (permission: UserPermission): boolean => {
    return permissions.value.includes(permission)
  }

  const hasAnyPermission = (permissionList: UserPermission[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissionList: UserPermission[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }

  const clearUserData = (): void => {
    user.value = null
    token.value = ''
    refreshToken.value = ''
    loginTime.value = null
    
    // 清除本地存储
    storage.removeToken()
    storage.removeRefreshToken()
    storage.removeUser()
  }

  const getSessionDuration = (): number => {
    if (!loginTime.value) return 0
    return Date.now() - loginTime.value.getTime()
  }

  const isSessionExpired = (): boolean => {
    const maxSessionTime = 8 * 60 * 60 * 1000 // 8小时
    return getSessionDuration() > maxSessionTime
  }

  // 返回 store
  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    loginTime: readonly(loginTime),
    
    // 计算属性
    isAuthenticated,
    userLevel,
    permissions,
    isAdmin,
    canWrite,
    canRead,
    
    // 方法
    login,
    logout,
    refreshAccessToken,
    checkAuthStatus,
    restoreAuthState,
    updateUserInfo,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    clearUserData,
    getSessionDuration,
    isSessionExpired
  }
})
