<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧背景区域 -->
      <div class="login-background">
        <div class="background-content">
          <h1 class="system-title">太享查询系统</h1>
          <p class="system-subtitle">现代化金融数据查询与分析平台</p>
          <div class="feature-list">
            <div class="feature-item">
              <el-icon><DataAnalysis /></el-icon>
              <span>智能数据分析</span>
            </div>
            <div class="feature-item">
              <el-icon><Document /></el-icon>
              <span>便捷报表导出</span>
            </div>
            <div class="feature-item">
              <el-icon><Monitor /></el-icon>
              <span>实时数据监控</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form-wrapper">
          <div class="login-header">
            <img src="/logo.png" alt="Logo" class="logo" />
            <h2>用户登录</h2>
            <p>请输入您的账号和密码</p>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            size="large"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="loginForm.remember">
                  记住我
                </el-checkbox>
                <el-link type="primary" :underline="false">
                  忘记密码？
                </el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="loading"
                @click="handleLogin"
              >
                {{ loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 快速登录提示 -->
          <div class="quick-login-tips">
            <el-divider>快速登录</el-divider>
            <div class="demo-accounts">
              <el-tag
                v-for="account in demoAccounts"
                :key="account.username"
                class="demo-account"
                :type="account.type"
                @click="fillDemoAccount(account)"
              >
                {{ account.label }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本信息 -->
    <div class="version-info">
      <span>太享查询系统 v2.0.0</span>
      <span>© 2024 HDSC Team</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import type { LoginCredentials } from '@/types/user'

// 路由
const router = useRouter()
const route = useRoute()

// Store
const userStore = useUserStore()

// 响应式数据
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

const loginForm = reactive<LoginCredentials>({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, message: '密码不能为空', trigger: 'blur' }
  ]
}

// 演示账号
const demoAccounts = [
  {
    username: 'Doolin',
    password: 'Doolin',
    label: '管理员账号',
    type: 'danger' as const
  },
  {
    username: '881017',
    password: '881017',
    label: '标准用户',
    type: 'warning' as const
  },
  {
    username: 'TT2024',
    password: 'TT2024',
    label: '受限用户',
    type: 'info' as const
  }
]

// 方法
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()
    
    loading.value = true

    // 执行登录
    const success = await userStore.login(loginForm)
    
    if (success) {
      // 登录成功，跳转到目标页面
      const redirect = (route.query.redirect as string) || '/dashboard'
      await router.push(redirect)
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

const fillDemoAccount = (account: typeof demoAccounts[0]) => {
  loginForm.username = account.username
  loginForm.password = account.password
  ElMessage.info(`已填入${account.label}信息`)
}

// 生命周期
onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isAuthenticated) {
    router.push('/dashboard')
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.login-wrapper {
  width: 100%;
  max-width: 1000px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  min-height: 600px;
}

.login-background {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: white;
}

.background-content {
  text-align: center;
  max-width: 400px;
}

.system-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  line-height: 1.6;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  
  .el-icon {
    font-size: 1.5rem;
  }
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
  }
  
  h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  p {
    color: #909399;
    font-size: 0.9rem;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
}

.quick-login-tips {
  margin-top: 30px;
  text-align: center;
  
  .el-divider {
    margin: 20px 0;
    
    :deep(.el-divider__text) {
      color: #909399;
      font-size: 0.85rem;
    }
  }
}

.demo-accounts {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.demo-account {
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.version-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

// 响应式设计
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    max-width: 400px;
    min-height: auto;
  }
  
  .login-background {
    padding: 30px 20px;
    
    .system-title {
      font-size: 2rem;
    }
    
    .feature-list {
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .feature-item {
      flex-direction: column;
      text-align: center;
      gap: 8px;
      font-size: 0.9rem;
    }
  }
  
  .login-form-container {
    padding: 30px 20px;
  }
  
  .version-info {
    flex-direction: column;
    text-align: center;
    gap: 5px;
  }
}
</style>
