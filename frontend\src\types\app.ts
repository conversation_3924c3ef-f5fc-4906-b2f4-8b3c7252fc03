/**
 * 应用相关类型定义
 */

// 应用配置
export interface AppConfig {
  title: string
  version: string
  apiBaseUrl: string
  pageSize: number
  maxPageSize: number
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
}

// 窗口尺寸
export interface WindowSize {
  width: number
  height: number
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 面包屑项
export interface BreadcrumbItem {
  title: string
  path: string
  icon?: string
}

// 菜单项
export interface MenuItem {
  id: string
  title: string
  path?: string
  icon?: string
  children?: MenuItem[]
  permissions?: string[]
  badge?: string | number
  disabled?: boolean
  hidden?: boolean
  external?: boolean
  target?: '_blank' | '_self'
}

// 通知类型
export type NotificationType = 'success' | 'warning' | 'info' | 'error'

// 通知项
export interface NotificationItem {
  id: string
  type: NotificationType
  title: string
  message: string
  timestamp: string
  read: boolean
  actions?: NotificationAction[]
  autoClose?: boolean
  duration?: number
}

// 通知操作
export interface NotificationAction {
  label: string
  action: () => void
  type?: 'primary' | 'success' | 'warning' | 'danger'
}

// 加载状态
export interface LoadingState {
  global: boolean
  page: boolean
  component: Record<string, boolean>
}

// 错误信息
export interface ErrorInfo {
  code: string
  message: string
  details?: any
  timestamp: string
  stack?: string
}

// 分页信息
export interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// API 响应基础结构
export interface ApiResponse<T = any> {
  success: boolean
  code: string
  message: string
  timestamp: string
  data: T
  meta?: {
    pagination?: PaginationInfo
    [key: string]: any
  }
  errors?: string[]
  details?: any
}

// 表格列配置
export interface TableColumn {
  key: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: 'left' | 'right'
  sortable?: boolean
  filterable?: boolean
  searchable?: boolean
  formatter?: (value: any, row: any) => string
  render?: (value: any, row: any) => any
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  showOverflowTooltip?: boolean
  resizable?: boolean
  hidden?: boolean
}

// 表格配置
export interface TableConfig {
  columns: TableColumn[]
  data: any[]
  loading?: boolean
  pagination?: PaginationInfo
  selection?: boolean
  stripe?: boolean
  border?: boolean
  size?: 'large' | 'default' | 'small'
  height?: number | string
  maxHeight?: number | string
  showHeader?: boolean
  highlightCurrentRow?: boolean
  rowKey?: string
  defaultSort?: {
    prop: string
    order: 'ascending' | 'descending'
  }
}

// 搜索条件
export interface SearchCriteria {
  keyword?: string
  filters?: Record<string, any>
  dateRange?: {
    start: string
    end: string
  }
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  page?: number
  pageSize?: number
}

// 导出选项
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  filename?: string
  fields?: string[]
  filters?: SearchCriteria
  includeHeaders?: boolean
  sheetName?: string
}

// 图表配置
export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'radar' | 'scatter'
  data: any
  options?: any
  width?: number
  height?: number
  responsive?: boolean
  maintainAspectRatio?: boolean
}

// 主题配置
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto'
  primaryColor: string
  successColor: string
  warningColor: string
  dangerColor: string
  infoColor: string
  fontSize: 'small' | 'medium' | 'large'
  borderRadius: 'none' | 'small' | 'medium' | 'large'
  animation: boolean
}

// 布局配置
export interface LayoutConfig {
  sidebar: {
    collapsed: boolean
    width: number
    collapsedWidth: number
  }
  header: {
    height: number
    fixed: boolean
  }
  footer: {
    height: number
    show: boolean
  }
  tabs: {
    show: boolean
    persistent: boolean
  }
}

// 快捷键配置
export interface ShortcutKey {
  key: string
  ctrl?: boolean
  alt?: boolean
  shift?: boolean
  meta?: boolean
  description: string
  action: () => void
  disabled?: boolean
}

// 系统状态
export interface SystemStatus {
  online: boolean
  serverTime: string
  version: string
  environment: 'development' | 'staging' | 'production'
  maintenance: boolean
  features: Record<string, boolean>
}

// 用户偏好设置
export interface UserPreferences {
  theme: ThemeConfig
  layout: LayoutConfig
  language: string
  timezone: string
  dateFormat: string
  timeFormat: string
  numberFormat: string
  shortcuts: ShortcutKey[]
  notifications: {
    desktop: boolean
    email: boolean
    sound: boolean
  }
}
