import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import { setupGlobalComponents } from './components'
import { setupGlobalDirectives } from './directives'
import { setupGlobalProperties } from './utils/global'

// 导入全局样式
import '@/styles/index.scss'

// 创建应用实例
const app = createApp(App)

// 创建 Pinia 实例
const pinia = createPinia()

// 注册 Element Plus
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册 Pinia
app.use(pinia)

// 注册路由
app.use(router)

// 注册全局组件
setupGlobalComponents(app)

// 注册全局指令
setupGlobalDirectives(app)

// 注册全局属性
setupGlobalProperties(app)

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 太享查询系统前端启动成功')
  console.log('📦 Vue版本:', app.version)
  console.log('🌐 环境:', import.meta.env.MODE)
}
