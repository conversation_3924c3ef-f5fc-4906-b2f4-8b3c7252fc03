"""
客户管理 API 路由
处理客户相关的数据查询和管理功能
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import logging

from backend.app.services.customer_service import CustomerService
from backend.app.utils.validators import (
    validate_pagination_params, validate_customer_name, 
    validate_date_range, validate_search_query
)
from backend.app.utils.responses import (
    success_response, error_response, paginated_response,
    validation_error_response, not_found_response
)

logger = logging.getLogger(__name__)

# 创建客户蓝图
customers_bp = Blueprint('customers', __name__)

# 初始化客户服务
customer_service = CustomerService()


@customers_bp.route('/', methods=['GET'])
@jwt_required()
def get_customers():
    """获取客户列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1)
        page_size = request.args.get('page_size', 20)
        keyword = request.args.get('keyword', '')
        
        # 验证分页参数
        pagination_validation = validate_pagination_params(page, page_size)
        if not pagination_validation['valid']:
            return validation_error_response(pagination_validation['errors']), 400
        
        page = pagination_validation['data']['page']
        page_size = pagination_validation['data']['page_size']
        
        # 验证搜索关键词
        if keyword:
            search_validation = validate_search_query(keyword)
            if not search_validation['valid']:
                return validation_error_response(search_validation['errors']), 400
            keyword = search_validation['data']['query']
        
        # 获取当前用户
        current_user = get_jwt_identity()
        
        # 查询客户数据
        result = customer_service.get_customers(
            page=page,
            page_size=page_size,
            keyword=keyword,
            user=current_user
        )
        
        if result['success']:
            return paginated_response(
                data=result['data'],
                page=page,
                page_size=page_size,
                total=result['total'],
                message='获取客户列表成功'
            )
        else:
            return error_response(
                message=result['message'],
                code='QUERY_ERROR'
            ), 500
            
    except Exception as e:
        logger.error(f'获取客户列表失败: {str(e)}')
        return error_response(
            message='获取客户列表失败，请稍后重试',
            code='INTERNAL_ERROR'
        ), 500


@customers_bp.route('/<customer_name>', methods=['GET'])
@jwt_required()
def get_customer_detail(customer_name):
    """获取客户详情"""
    try:
        # 验证客户名称
        name_validation = validate_customer_name(customer_name)
        if not name_validation['valid']:
            return validation_error_response(name_validation['errors']), 400
        
        customer_name = name_validation['data']['customer_name']
        current_user = get_jwt_identity()
        
        # 获取客户详情
        result = customer_service.get_customer_detail(customer_name, current_user)
        
        if result['success']:
            if result['data']:
                return success_response(
                    data=result['data'],
                    message='获取客户详情成功'
                )
            else:
                return not_found_response('客户'), 404
        else:
            return error_response(
                message=result['message'],
                code='QUERY_ERROR'
            ), 500
            
    except Exception as e:
        logger.error(f'获取客户详情失败: {str(e)}')
        return error_response(
            message='获取客户详情失败，请稍后重试',
            code='INTERNAL_ERROR'
        ), 500


@customers_bp.route('/<customer_name>/summary', methods=['GET'])
@jwt_required()
def get_customer_summary(customer_name):
    """获取客户汇总信息"""
    try:
        # 验证客户名称
        name_validation = validate_customer_name(customer_name)
        if not name_validation['valid']:
            return validation_error_response(name_validation['errors']), 400
        
        customer_name = name_validation['data']['customer_name']
        current_user = get_jwt_identity()
        
        # 获取客户汇总
        result = customer_service.get_customer_summary(customer_name, current_user)
        
        if result['success']:
            if result['data']:
                return success_response(
                    data=result['data'],
                    message='获取客户汇总成功'
                )
            else:
                return not_found_response('客户汇总数据'), 404
        else:
            return error_response(
                message=result['message'],
                code='QUERY_ERROR'
            ), 500
            
    except Exception as e:
        logger.error(f'获取客户汇总失败: {str(e)}')
        return error_response(
            message='获取客户汇总失败，请稍后重试',
            code='INTERNAL_ERROR'
        ), 500


@customers_bp.route('/<customer_name>/orders', methods=['GET'])
@jwt_required()
def get_customer_orders(customer_name):
    """获取客户订单列表"""
    try:
        # 验证客户名称
        name_validation = validate_customer_name(customer_name)
        if not name_validation['valid']:
            return validation_error_response(name_validation['errors']), 400
        
        customer_name = name_validation['data']['customer_name']
        
        # 获取查询参数
        page = request.args.get('page', 1)
        page_size = request.args.get('page_size', 20)
        status = request.args.get('status', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        # 验证分页参数
        pagination_validation = validate_pagination_params(page, page_size)
        if not pagination_validation['valid']:
            return validation_error_response(pagination_validation['errors']), 400
        
        page = pagination_validation['data']['page']
        page_size = pagination_validation['data']['page_size']
        
        # 验证日期范围
        if start_date or end_date:
            date_validation = validate_date_range(start_date, end_date)
            if not date_validation['valid']:
                return validation_error_response(date_validation['errors']), 400
        
        current_user = get_jwt_identity()
        
        # 获取客户订单
        result = customer_service.get_customer_orders(
            customer_name=customer_name,
            page=page,
            page_size=page_size,
            status=status,
            start_date=start_date,
            end_date=end_date,
            user=current_user
        )
        
        if result['success']:
            return paginated_response(
                data=result['data'],
                page=page,
                page_size=page_size,
                total=result['total'],
                message='获取客户订单成功'
            )
        else:
            return error_response(
                message=result['message'],
                code='QUERY_ERROR'
            ), 500
            
    except Exception as e:
        logger.error(f'获取客户订单失败: {str(e)}')
        return error_response(
            message='获取客户订单失败，请稍后重试',
            code='INTERNAL_ERROR'
        ), 500


@customers_bp.route('/search', methods=['POST'])
@jwt_required()
def search_customers():
    """搜索客户"""
    try:
        data = request.get_json()
        
        if not data:
            return validation_error_response(['请求数据不能为空']), 400
        
        # 获取搜索参数
        keyword = data.get('keyword', '')
        filters = data.get('filters', {})
        page = data.get('page', 1)
        page_size = data.get('page_size', 20)
        
        # 验证搜索关键词
        if keyword:
            search_validation = validate_search_query(keyword)
            if not search_validation['valid']:
                return validation_error_response(search_validation['errors']), 400
            keyword = search_validation['data']['query']
        
        # 验证分页参数
        pagination_validation = validate_pagination_params(page, page_size)
        if not pagination_validation['valid']:
            return validation_error_response(pagination_validation['errors']), 400
        
        page = pagination_validation['data']['page']
        page_size = pagination_validation['data']['page_size']
        
        current_user = get_jwt_identity()
        
        # 执行搜索
        result = customer_service.search_customers(
            keyword=keyword,
            filters=filters,
            page=page,
            page_size=page_size,
            user=current_user
        )
        
        if result['success']:
            return paginated_response(
                data=result['data'],
                page=page,
                page_size=page_size,
                total=result['total'],
                message='搜索客户成功'
            )
        else:
            return error_response(
                message=result['message'],
                code='SEARCH_ERROR'
            ), 500
            
    except Exception as e:
        logger.error(f'搜索客户失败: {str(e)}')
        return error_response(
            message='搜索客户失败，请稍后重试',
            code='INTERNAL_ERROR'
        ), 500
