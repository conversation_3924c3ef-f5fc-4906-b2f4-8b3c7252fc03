"""
认证服务
处理用户认证、权限验证等业务逻辑
"""

import hashlib
import logging
from datetime import datetime
from typing import Dict, Optional, List

from backend.config.config import Config

logger = logging.getLogger(__name__)


class AuthService:
    """认证服务类"""
    
    def __init__(self):
        self.config = Config()
        # 模拟用户数据库（生产环境应使用真实数据库）
        self.users = {
            'TT2024': {
                'password_hash': self._hash_password('TT2024'),  # 简化示例
                'level': 'limited',
                'permissions': ['read'],
                'created_at': '2024-01-01',
                'last_login': None
            },
            '881017': {
                'password_hash': self._hash_password('881017'),
                'level': 'standard',
                'permissions': ['read', 'write'],
                'created_at': '2024-01-01',
                'last_login': None
            },
            'Doolin': {
                'password_hash': self._hash_password('Doolin'),
                'level': 'full',
                'permissions': ['read', 'write', 'admin'],
                'created_at': '2024-01-01',
                'last_login': None
            }
        }
    
    def _hash_password(self, password: str) -> str:
        """密码哈希（简化版本，生产环境应使用更安全的方法）"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """验证用户凭据"""
        try:
            user = self.users.get(username)
            if not user:
                logger.warning(f'用户不存在: {username}')
                return None
            
            # 验证密码
            password_hash = self._hash_password(password)
            if user['password_hash'] != password_hash:
                logger.warning(f'密码错误: {username}')
                return None
            
            # 更新最后登录时间
            user['last_login'] = datetime.now().isoformat()
            
            logger.info(f'用户认证成功: {username}')
            return {
                'username': username,
                'level': user['level'],
                'permissions': user['permissions'],
                'last_login': user['last_login']
            }
            
        except Exception as e:
            logger.error(f'用户认证失败: {str(e)}')
            return None
    
    def get_user_info(self, username: str) -> Optional[Dict]:
        """获取用户信息"""
        try:
            user = self.users.get(username)
            if not user:
                return None
            
            return {
                'username': username,
                'level': user['level'],
                'permissions': user['permissions'],
                'created_at': user['created_at'],
                'last_login': user['last_login']
            }
            
        except Exception as e:
            logger.error(f'获取用户信息失败: {str(e)}')
            return None
    
    def check_permission(self, username: str, required_permission: str) -> bool:
        """检查用户权限"""
        try:
            user = self.users.get(username)
            if not user:
                return False
            
            return required_permission in user['permissions']
            
        except Exception as e:
            logger.error(f'权限检查失败: {str(e)}')
            return False
    
    def get_user_level(self, username: str) -> str:
        """获取用户权限级别"""
        user = self.users.get(username)
        return user['level'] if user else 'limited'
    
    def is_admin(self, username: str) -> bool:
        """检查是否为管理员"""
        return self.check_permission(username, 'admin')
    
    def can_write(self, username: str) -> bool:
        """检查是否有写权限"""
        return self.check_permission(username, 'write')
    
    def can_read(self, username: str) -> bool:
        """检查是否有读权限"""
        return self.check_permission(username, 'read')
    
    def get_all_users(self) -> List[Dict]:
        """获取所有用户列表（管理员功能）"""
        try:
            users_list = []
            for username, user_data in self.users.items():
                users_list.append({
                    'username': username,
                    'level': user_data['level'],
                    'permissions': user_data['permissions'],
                    'created_at': user_data['created_at'],
                    'last_login': user_data['last_login']
                })
            return users_list
            
        except Exception as e:
            logger.error(f'获取用户列表失败: {str(e)}')
            return []
    
    def create_user(self, username: str, password: str, level: str = 'limited') -> bool:
        """创建新用户（管理员功能）"""
        try:
            if username in self.users:
                logger.warning(f'用户已存在: {username}')
                return False
            
            # 根据级别设置权限
            permissions_map = {
                'limited': ['read'],
                'standard': ['read', 'write'],
                'full': ['read', 'write', 'admin']
            }
            
            self.users[username] = {
                'password_hash': self._hash_password(password),
                'level': level,
                'permissions': permissions_map.get(level, ['read']),
                'created_at': datetime.now().isoformat(),
                'last_login': None
            }
            
            logger.info(f'用户创建成功: {username}')
            return True
            
        except Exception as e:
            logger.error(f'用户创建失败: {str(e)}')
            return False
    
    def update_user_password(self, username: str, new_password: str) -> bool:
        """更新用户密码"""
        try:
            if username not in self.users:
                logger.warning(f'用户不存在: {username}')
                return False
            
            self.users[username]['password_hash'] = self._hash_password(new_password)
            logger.info(f'密码更新成功: {username}')
            return True
            
        except Exception as e:
            logger.error(f'密码更新失败: {str(e)}')
            return False
    
    def delete_user(self, username: str) -> bool:
        """删除用户（管理员功能）"""
        try:
            if username not in self.users:
                logger.warning(f'用户不存在: {username}')
                return False
            
            del self.users[username]
            logger.info(f'用户删除成功: {username}')
            return True
            
        except Exception as e:
            logger.error(f'用户删除失败: {str(e)}')
            return False
