"""
Flask API 配置文件
现代化配置管理，支持多环境部署
"""

import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev_key_please_change_in_production')
    
    # API 配置
    API_KEY = os.getenv('API_KEY', 'lxw8025031')
    API_BASE_URL = os.getenv('API_BASE_URL', 'http://115.190.29.10:5000')
    
    # JWT 配置
    JWT_SECRET_KEY = SECRET_KEY
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=8)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'
    
    # 缓存配置
    CACHE_TYPE = 'SimpleCache'
    CACHE_DEFAULT_TIMEOUT = 300
    CACHE_THRESHOLD = 500
    
    # 缓存超时配置（秒）
    CACHE_TIMEOUTS = {
        'filter_data': 300,
        'filter_overdue_orders': 600,
        'filter_orders_by_customer_name': 600,
        'summary_data': 1800,
        'order_summary': 1800,
        'customer_summary': 600
    }
    
    # 用户权限配置
    USER_LEVELS = {
        'TT2024': 'limited',
        '881017': 'standard',
        'Doolin': 'full'
    }
    
    # 状态颜色映射
    STATUS_COLORS = {
        '逾期还款': '#FFFFEB9C',
        '提前还款': '#FFD9E1F2',
        '按时还款': '#FFC6EFCE',
        '账单日': '#FFF4B084',
        '逾期未还': '#FFFFC7CE',
        '无效日期': '#FFDEDEDE',
        '未到还款日期': '#FFFFFFFF',
        '催收': '#FFFFEB9C',
        '诉讼': '#FFFFC7CE',
        '未知状态': '#FFC0C0C0'
    }
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls'}
    
    @staticmethod
    def is_valid_api_key(key):
        """检查 API 密钥是否有效"""
        return key is not None and key != "" and key != "sdsdsd"


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'
    
    # 开发环境数据库（如果需要）
    # SQLALCHEMY_DATABASE_URI = 'sqlite:///dev.db'
    
    # 开发环境日志级别
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_ENV = 'production'
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 生产环境日志级别
    LOG_LEVEL = 'INFO'
    
    # 生产环境数据库
    # SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL')


class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    
    # 测试数据库
    # SQLALCHEMY_DATABASE_URI = 'sqlite:///test.db'
    
    # 测试环境缓存
    CACHE_TYPE = 'NullCache'  # 测试时不使用缓存


# 配置映射
config_by_name = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

# 获取当前配置
def get_config():
    """获取当前环境配置"""
    config_name = os.getenv('FLASK_ENV', 'development')
    return config_by_name.get(config_name, DevelopmentConfig)
